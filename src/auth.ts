import { Svelte<PERSON>itAuth } from '@auth/sveltekit';
import Zitadel from '@auth/core/providers/zitadel';
import {
	AUTH_SECRET,
	ZITADEL_ISSUER,
	ZITADEL_CLIENT_ID,
	ZITADEL_CLIENT_SECRET
} from '$env/static/private';
import { PUBLIC_APP_URL } from '$env/static/public';
import type { Profile, Session, User } from '@auth/core/types';

// Extended user profile type for Zitadel
export interface ZitadelProfile extends Profile {
	sub: string;
	name: string;
	given_name: string;
	family_name: string;
	email: string;
	email_verified: boolean;
	picture?: string;
	preferred_username: string;
	locale?: string;
	phone?: string;
	phone_verified?: boolean;
}

// Extended session type with user information
export interface ExtendedSession extends Session {
	user: {
		id: string;
		name?: string | null;
		email?: string | null;
		image?: string | null;
		given_name?: string;
		family_name?: string;
		preferred_username?: string;
		email_verified?: boolean;
	};
}

export const { handle, signIn, signOut } = SvelteKitAuth({
	// Authentication providers
	providers: [
		Zitadel({
			clientId: ZITADEL_CLIENT_ID,
			clientSecret: ZITADEL_CLIENT_SECRET,
			issuer: ZITADEL_ISSUER,
			// Request additional scopes for user information
			authorization: {
				params: {
					scope: 'openid profile email offline_access urn:zitadel:iam:org:project:id:zitadel:aud'
				}
			},
			// Custom profile mapping to extract additional user data
			profile(profile: ZitadelProfile) {
				return {
					id: profile.sub,
					name: profile.name || `${profile.given_name} ${profile.family_name}`,
					email: profile.email,
					image: profile.picture,
					given_name: profile.given_name,
					family_name: profile.family_name,
					preferred_username: profile.preferred_username,
					email_verified: profile.email_verified
				};
			}
		})
	],

	// Session configuration
	session: {
		strategy: 'jwt',
		maxAge: 30 * 24 * 60 * 60, // 30 days
		updateAge: 24 * 60 * 60 // 24 hours
	},

	// JWT configuration
	jwt: {
		maxAge: 30 * 24 * 60 * 60 // 30 days
	},

	// Callback configuration
	callbacks: {
		// JWT callback - runs whenever a JWT is created, updated, or accessed
		async jwt({ token, user, account, profile }) {
			// Initial sign in
			if (user && account && profile) {
				const zitadelProfile = profile as ZitadelProfile;
				token.sub = user.id;
				token.name = user.name;
				token.email = user.email;
				token.picture = user.image;
				token.given_name = zitadelProfile.given_name;
				token.family_name = zitadelProfile.family_name;
				token.preferred_username = zitadelProfile.preferred_username;
				token.email_verified = zitadelProfile.email_verified;
				token.accessToken = account.access_token;
				token.refreshToken = account.refresh_token;
			}
			return token;
		},

		// Session callback - runs whenever a session is checked
		async session({ session, token }) {
			if (token) {
				session.user.id = token.sub as string;
				session.user.name = token.name as string;
				session.user.email = token.email as string;
				session.user.image = token.picture as string;
				(session.user as any).given_name = token.given_name;
				(session.user as any).family_name = token.family_name;
				(session.user as any).preferred_username = token.preferred_username;
				(session.user as any).email_verified = token.email_verified;
			}
			return session as ExtendedSession;
		},

		// Sign in callback - controls whether a user is allowed to sign in
		async signIn({ user, account, profile }) {
			// Allow sign in for verified email addresses
			if (account?.provider === 'zitadel') {
				const zitadelProfile = profile as ZitadelProfile;
				return zitadelProfile.email_verified === true;
			}
			return true;
		},

		// Redirect callback - controls where users are redirected after sign in/out
		async redirect({ url, baseUrl }) {
			// Allows relative callback URLs
			if (url.startsWith('/')) return `${baseUrl}${url}`;
			// Allows callback URLs on the same origin
			else if (new URL(url).origin === baseUrl) return url;
			return baseUrl;
		}
	},

	// Pages configuration
	pages: {
		signIn: '/auth/signin',
		error: '/auth/error'
	},

	// Events for logging and analytics
	events: {
		async signIn({ user, account, profile, isNewUser }) {
			console.log('User signed in:', { 
				userId: user.id, 
				email: user.email, 
				isNewUser,
				provider: account?.provider 
			});
		},
		async signOut({ session, token }) {
			console.log('User signed out:', { userId: session?.user?.id });
		}
	},

	// Debug mode for development
	debug: process.env.NODE_ENV === 'development',

	// Secret for JWT signing and encryption
	secret: AUTH_SECRET,

	// Trust host for deployment
	trustHost: true
});
