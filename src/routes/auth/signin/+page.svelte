<script lang="ts">
	import { signIn } from '@auth/sveltekit/client';
	import { page } from '$app/stores';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card/index.js';

	// Get the callback URL from query parameters
	$: callbackUrl = $page.url.searchParams.get('callbackUrl') || '/';
	$: error = $page.url.searchParams.get('error');

	// Error messages mapping
	const errorMessages: Record<string, string> = {
		Signin: 'Try signing in with a different account.',
		OAuthSignin: 'Try signing in with a different account.',
		OAuthCallback: 'Try signing in with a different account.',
		OAuthCreateAccount: 'Try signing in with a different account.',
		EmailCreateAccount: 'Try signing in with a different account.',
		Callback: 'Try signing in with a different account.',
		OAuthAccountNotLinked: 'To confirm your identity, sign in with the same account you used originally.',
		EmailSignin: 'The e-mail could not be sent.',
		CredentialsSignin: 'Sign in failed. Check the details you provided are correct.',
		SessionRequired: 'Please sign in to access this page.',
		default: 'Unable to sign in.'
	};

	function handleSignIn() {
		signIn('zitadel', { callbackUrl });
	}
</script>

<svelte:head>
	<title>Sign In - SvelteKit App</title>
</svelte:head>

<div class="container mx-auto flex min-h-screen items-center justify-center p-4">
	<Card class="w-full max-w-md">
		<CardHeader class="text-center">
			<CardTitle class="text-2xl font-bold">Welcome Back</CardTitle>
			<CardDescription>
				Sign in to your account to continue
			</CardDescription>
		</CardHeader>
		<CardContent class="space-y-4">
			{#if error}
				<div class="rounded-md bg-red-50 p-4 dark:bg-red-900/20">
					<div class="text-sm text-red-700 dark:text-red-400">
						{errorMessages[error] || errorMessages.default}
					</div>
				</div>
			{/if}

			<Button 
				on:click={handleSignIn}
				class="w-full"
				size="lg"
			>
				<svg class="mr-2 h-4 w-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
					<path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
					<path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
				</svg>
				Sign in with Zitadel
			</Button>

			<div class="text-center text-sm text-gray-600 dark:text-gray-400">
				By signing in, you agree to our terms of service and privacy policy.
			</div>
		</CardContent>
	</Card>
</div>
