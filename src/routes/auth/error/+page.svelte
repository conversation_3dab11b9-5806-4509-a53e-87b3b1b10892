<script lang="ts">
	import { page } from '$app/stores';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card/index.js';

	$: error = $page.url.searchParams.get('error');

	// Error messages mapping
	const errorMessages: Record<string, { title: string; description: string }> = {
		Configuration: {
			title: 'Server Error',
			description: 'There is a problem with the server configuration. Please contact support.'
		},
		AccessDenied: {
			title: 'Access Denied',
			description: 'You do not have permission to sign in. Please contact an administrator.'
		},
		Verification: {
			title: 'Verification Error',
			description: 'The verification token has expired or has already been used.'
		},
		Default: {
			title: 'Authentication Error',
			description: 'An error occurred during authentication. Please try again.'
		}
	};

	$: errorInfo = errorMessages[error || 'Default'] || errorMessages.Default;
</script>

<svelte:head>
	<title>Authentication Error - SvelteKit App</title>
</svelte:head>

<div class="container mx-auto flex min-h-screen items-center justify-center p-4">
	<Card class="w-full max-w-md">
		<CardHeader class="text-center">
			<div class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
				<svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
				</svg>
			</div>
			<CardTitle class="text-xl font-bold text-red-600 dark:text-red-400">
				{errorInfo.title}
			</CardTitle>
			<CardDescription class="text-center">
				{errorInfo.description}
			</CardDescription>
		</CardHeader>
		<CardContent class="space-y-4">
			{#if error}
				<div class="rounded-md bg-gray-50 p-3 dark:bg-gray-800">
					<div class="text-sm text-gray-600 dark:text-gray-400">
						<strong>Error Code:</strong> {error}
					</div>
				</div>
			{/if}

			<div class="flex flex-col gap-2">
				<Button href="/auth/signin" class="w-full">
					Try Again
				</Button>
				<Button href="/" variant="outline" class="w-full">
					Go Home
				</Button>
			</div>

			<div class="text-center text-sm text-gray-500 dark:text-gray-400">
				If this problem persists, please contact support.
			</div>
		</CardContent>
	</Card>
</div>
