import { handle as authHandle } from './auth.js';
import { sequence } from '@sveltejs/kit/hooks';
import type { Handle } from '@sveltejs/kit';

// Custom handle for additional server-side logic
const customHandle: Handle = async ({ event, resolve }) => {
	// You can add custom server-side logic here
	// For example, logging, custom headers, etc.
	
	return resolve(event);
};

// Combine the auth handle with any custom handles
export const handle = sequence(authHandle, customHandle);
