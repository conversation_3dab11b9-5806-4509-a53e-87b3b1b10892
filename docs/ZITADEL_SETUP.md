# Zitadel Setup Guide

This guide walks you through setting up a Zitadel application for authentication with your SvelteKit 5 application.

## Prerequisites

- Access to a Zitadel instance at `https://zitadel.local.acjzz.xyz`
- Admin privileges to create applications in Zitadel

## Step 1: Create a New Application

1. Log in to your Zitadel console at `https://zitadel.local.acjzz.xyz`
2. Navigate to your project (or create a new one)
3. Go to **Applications** section
4. Click **+ New Application**

## Step 2: Configure Application Settings

### Basic Information
- **Name**: `SvelteKit 5 App` (or your preferred name)
- **Type**: Select **Web Application**

### Authentication Method
- Choose **Authorization Code** flow
- Enable **PKCE** (Proof Key for Code Exchange) for enhanced security

### Redirect URIs
Add the following redirect URIs for your application:

**Development:**
```
http://localhost:5173/auth/callback/zitadel
```

**Production:**
```
https://your-domain.com/auth/callback/zitadel
```

### Post Logout Redirect URIs
Add the following post-logout redirect URIs:

**Development:**
```
http://localhost:5173
```

**Production:**
```
https://your-domain.com
```

## Step 3: Configure Scopes

Ensure the following scopes are enabled for your application:

### Required Scopes
- `openid` - Required for OpenID Connect
- `profile` - Access to user profile information
- `email` - Access to user email address

### Optional Scopes (recommended)
- `offline_access` - For refresh tokens
- `urn:zitadel:iam:org:project:id:zitadel:aud` - Zitadel specific audience

## Step 4: Application Settings

### Token Settings
- **Access Token Type**: JWT
- **Access Token Lifetime**: 3600 seconds (1 hour)
- **Refresh Token Lifetime**: 2592000 seconds (30 days)
- **ID Token Lifetime**: 3600 seconds (1 hour)

### Additional Settings
- **Enable Dev Mode**: ✅ (for development with localhost)
- **Skip Native App Success Page**: ✅ (recommended for web apps)

## Step 5: Obtain Client Credentials

After creating the application:

1. Copy the **Client ID** from the application details
2. Generate a **Client Secret** (if not auto-generated)
3. Update your `.env` file with these values:

```env
ZITADEL_CLIENT_ID=your-client-id-here
ZITADEL_CLIENT_SECRET=your-client-secret-here
```

## Step 6: User Management (Optional)

### Create Test Users
1. Go to **Users** section in Zitadel
2. Create test users for development
3. Assign appropriate roles/permissions

### Configure User Attributes
Ensure the following user attributes are available:
- Email (required)
- Given Name
- Family Name
- Picture/Avatar (optional)

## Security Considerations

1. **Client Secret**: Keep your client secret secure and never commit it to version control
2. **Redirect URIs**: Only add trusted redirect URIs to prevent authorization code interception
3. **HTTPS**: Always use HTTPS in production
4. **Dev Mode**: Disable dev mode in production environments

## Troubleshooting

### Common Issues

1. **Invalid Redirect URI**: Ensure the redirect URI in Zitadel exactly matches your application URL
2. **CORS Issues**: Zitadel handles CORS automatically for configured redirect URIs
3. **Token Validation**: Ensure your application can reach the Zitadel issuer URL

### Debug Information

Your Zitadel OIDC configuration is available at:
```
https://zitadel.local.acjzz.xyz/.well-known/openid_configuration
```

## Next Steps

After completing this setup:
1. Update your `.env` file with the client credentials
2. Test the authentication flow in your SvelteKit application
3. Configure user roles and permissions as needed

## Support

For Zitadel-specific issues, refer to:
- [Zitadel Documentation](https://zitadel.com/docs)
- [Zitadel Community](https://github.com/zitadel/zitadel/discussions)
